2025-08-27 14:28:58.730677: currentXmlPath:C:\Users\<USER>\Desktop\Release (1)\Release\packages\sea_socket\lib\src\protocols
2025-08-27 14:28:58.730677: 61
2025-08-27 14:28:58.737654: ✅ 数据库更新成功: planConfig
2025-08-27 14:28:58.738650: ✅ 数据库更新成功: SysConfig
2025-08-27 14:28:58.738650: ✅ 数据库更新成功: Sip2Config
2025-08-27 14:28:58.738650: ✅ 数据库更新成功: pageConfig
2025-08-27 14:28:58.738650: ✅ 数据库更新成功: readerConfig
2025-08-27 14:28:58.739647: ✅ 数据库更新成功: banZhengConfig
2025-08-27 14:28:58.739647: ✅ 数据库更新成功: printConfig
2025-08-27 14:28:58.956929: 开始初始化闸机协调器...
2025-08-27 14:28:58.957922: ✅ 已清除串口配置缓存，下次访问将重新从数据库读取
2025-08-27 14:28:58.957922: ✅ 已清除 SettingProvider 串口配置缓存
2025-08-27 14:28:58.973875: ✅ 通过 getSerialConfig 获取串口配置: COM1 @ 115200
2025-08-27 14:28:58.974865: ✅ 串口配置加载完成: COM1 @ 115200
2025-08-27 14:28:58.974865: 可用串口: [COM1, COM2, COM3, COM4, COM5, COM6]
2025-08-27 14:28:58.978850: 连接闸机串口: COM1
2025-08-27 14:28:58.978850: 尝试连接串口: COM1, 波特率: 115200
2025-08-27 14:28:58.979848: 串口连接成功: COM1 at 115200 baud
2025-08-27 14:28:58.979848: 开始监听串口数据
2025-08-27 14:28:58.979848: 串口连接状态变化: true
2025-08-27 14:28:58.980844: 闸机串口连接成功
2025-08-27 14:28:58.980844: 串口 COM1 连接成功 (波特率: 115200)
2025-08-27 14:28:58.980844: 闸机串口服务初始化成功: COM1
2025-08-27 14:28:58.981840: 开始监听串口数据（通过 GateSerialManager 事件流）
2025-08-27 14:28:58.981840: 开始监听闸机串口命令
2025-08-27 14:28:58.981840: 开始初始化RFID服务和共享池...
2025-08-27 14:28:58.981840: 开始初始化增强RFID服务...
2025-08-27 14:28:58.982837: 开始初始化增强RFID服务...
2025-08-27 14:28:58.982837: SIP2图书信息服务初始化完成
2025-08-27 14:28:58.982837: 增强RFID服务初始化完成，配置了1个阅读器
2025-08-27 14:28:58.983834: 📋 从数据库读取主从机配置: channel_2
2025-08-27 14:28:58.983834: 📋 配置详情: 从机模式
2025-08-27 14:28:58.983834: ⚠️ 从机模式：跳过RFID硬件扫描启动，等待主机数据同步
2025-08-27 14:28:58.984830: 📋 从机配置: 通道=channel_2, 主机地址=**************
2025-08-27 14:28:58.984830: 增强RFID服务初始化完成，持续扫描已启动
2025-08-27 14:28:58.985828: 📋 从数据库读取主从机配置: channel_2
2025-08-27 14:28:58.985828: 📋 配置详情: 从机模式
2025-08-27 14:28:58.985828: ⚠️ 从机模式：跳过持续数据收集，等待主机数据同步
2025-08-27 14:28:58.985828: 📋 从机配置: 通道=channel_2, 主机地址=**************
2025-08-27 14:28:58.986824: 开始全局初始化共享扫描池服务...
2025-08-27 14:28:58.986824: 共享扫描池已集成现有RFID服务
2025-08-27 14:28:58.986824: 📡 初始化后RFID扫描状态: scanning=false
2025-08-27 14:28:58.986824: 🔄 开始重置已处理条码集合...
2025-08-27 14:28:58.987820: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 14:28:58.987820: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 14:28:58.987820: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 14:28:58.987820: 📊 当前tagList状态: 0个标签
2025-08-27 14:28:58.987820: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 14:28:58.988817: 共享扫描池服务全局初始化完成
2025-08-27 14:28:58.988817: 🚀 初始化新架构服务...
2025-08-27 14:28:58.988817: 🖥️ 主机模式：使用主机集合A服务
2025-08-27 14:28:58.989814: ⏹️ 书籍信息查询服务停止监听
2025-08-27 14:28:58.989814: 🚀 书籍信息查询服务开始监听集合A变化
2025-08-27 14:28:58.989814: ✅ 新架构服务初始化完成
2025-08-27 14:28:58.989814: RFID服务和共享池初始化完成，持续扫描已启动
2025-08-27 14:28:58.990811: 闸机协调器初始化完成
2025-08-27 14:28:58.990811: 🔧 开始初始化主从机扩展（使用持久化配置）...
2025-08-27 14:28:58.990811: 开始初始化主从机扩展...
2025-08-27 14:28:58.991808: 从 seasetting 数据库加载主从机配置成功: channel_2
2025-08-27 14:28:58.991808: 配置详情: 从机模式
2025-08-27 14:28:58.992804: 📡 从 SettingProvider 获取串口配置: COM1 @ 115200
2025-08-27 14:28:58.992804: ✅ 通过 SettingProvider 加载串口配置成功
2025-08-27 14:28:58.992804: 启用主从机扩展: channel_2 (从机)
2025-08-27 14:28:58.993801: ✅ 数据变化通知流已创建
2025-08-27 14:28:58.993801: 共享扫描池已集成现有RFID服务
2025-08-27 14:28:58.993801: 📡 初始化后RFID扫描状态: scanning=false
2025-08-27 14:28:58.993801: 🔄 开始重置已处理条码集合...
2025-08-27 14:28:58.994797: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 14:28:58.994797: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 14:28:58.994797: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 14:28:58.994797: 📊 当前tagList状态: 0个标签
2025-08-27 14:28:58.994797: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 14:28:58.995794: 配置为从机模式: **************:8888
2025-08-27 14:28:58.997793: 成功连接到主机: **************:8888
2025-08-27 14:28:58.998785: 从机模式配置完成
2025-08-27 14:28:58.998785: [channel_2] 已集成现有GateCoordinator，开始监听事件
2025-08-27 14:28:58.998785: 主从机扩展启用成功
2025-08-27 14:28:58.999781: 主从机扩展初始化完成
2025-08-27 14:28:58.999781: 配置信息: MasterSlaveConfig(channelId: channel_2, isMaster: false, slaveAddress: null, masterAddress: **************, port: 8888)
2025-08-27 14:28:58.999781: ✅ 加载到持久化配置: 从机模式, 通道: channel_2
2025-08-27 14:28:58.999781: 主从机扩展初始化完成
2025-08-27 14:28:58.999781: 安全闸机系统初始化完成
2025-08-27 14:28:59.000777: 未知的主机消息类型: sync_barcodes
2025-08-27 14:28:59.011741: 开始初始化MultiAuthManager...
2025-08-27 14:28:59.012745: 多认证管理器状态变更: initializing
2025-08-27 14:28:59.012745: 认证优先级管理器: 开始加载认证方式
2025-08-27 14:28:59.013735: 配置的排序: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-27 14:28:59.013735: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝二维码认证, 芝麻信用码认证, 支付宝二维码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-08-27 14:28:59.013735: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-08-27 14:28:59.013735: 认证优先级管理器: 最终排序结果: 读者证
2025-08-27 14:28:59.014731: 认证优先级管理器: 主要认证方式: 读者证
2025-08-27 14:28:59.014731: 多认证管理器: 从优先级管理器加载的认证方式: 读者证
2025-08-27 14:28:59.014731: 多认证管理器: 当前默认显示方式: 读者证
2025-08-27 14:28:59.014731: 初始化读卡器认证服务
2025-08-27 14:28:59.014731: 读卡器认证服务初始化成功
2025-08-27 14:28:59.015728: 初始化共享读卡器认证服务
2025-08-27 14:28:59.015728: 读者证 认证服务初始化成功
2025-08-27 14:28:59.016724: 认证服务初始化完成，共初始化 1 种认证方式
2025-08-27 14:28:59.016724: 多认证管理器状态变更: idle
2025-08-27 14:28:59.016724: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.readerCard]
2025-08-27 14:28:59.016724: MultiAuthManager初始化完成
2025-08-27 14:28:59.017721: 开始初始化SilencePageViewModel...
2025-08-27 14:28:59.017721: 闸机串口服务已经初始化
2025-08-27 14:28:59.017721: 开始初始化闸机认证服务...
2025-08-27 14:28:59.017721: 闸机认证服务初始化完成，启用认证方式: 人脸识别、读者证、AuthMethod.wechatScanQRCode
2025-08-27 14:28:59.018718: RFID服务已经初始化
2025-08-27 14:28:59.018718: SIP2图书信息服务初始化完成
2025-08-27 14:28:59.018718: 💡 主从机扩展已准备就绪，请通过配置页面手动启用
2025-08-27 14:28:59.019714: 💡 可以通过MasterSlaveConfigPage进行配置
2025-08-27 14:28:59.019714: ✅ 统一事件监听已设置：SilencePageViewModel → GateCoordinator.eventStream
2025-08-27 14:28:59.019714: 串口监听已经启动
2025-08-27 14:28:59.019714: SilencePageViewModel初始化完成
2025-08-27 14:28:59.021708: socket 连接成功,isBroadcast:false
2025-08-27 14:28:59.021708: changeSocketStatus:true
2025-08-27 14:28:59.021708: Sip2HeartBeatManager start loginACS:false askACS:false
2025-08-27 14:28:59.021708: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-08-27 14:28:59.196127: Rsp : 941AY1AZFDFC
2025-08-27 14:28:59.207091: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-08-27 14:28:59.207091: Sip2HeartBeatManager start loginACS:false askACS:true
2025-08-27 14:28:59.208087: 发送心跳
2025-08-27 14:28:59.208087: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-08-27 14:28:59.359582: dispose IndexPage
2025-08-27 14:28:59.360581: IndexPage dispose
2025-08-27 14:28:59.501113: Rsp : 98YYYNNN00500320250827    1428452.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD518
2025-08-27 14:28:59.521045: 🔍 主从机模式检测: 启用=true, 主机模式=false
2025-08-27 14:28:59.522043: 🔍 扩展详细状态: {enabled: true, channel_id: channel_2, is_master: false, data_stream_ready: true, data_stream_exists: true, data_stream_closed: false, shared_pool_size: 0, queue_size: 0, comm_connected: true, timestamp: 2025-08-27T14:28:59.521045}
2025-08-27 14:28:59.522043: 🎯 检测到从机模式，设置数据监听
2025-08-27 14:28:59.522043: 🔧 开始设置从机持续数据监听...
2025-08-27 14:28:59.523040: ✅ 从机持续数据监听已设置
2025-08-27 14:29:07.608404: 接收到数据: aa 00 c8 80 00 00 27 82
2025-08-27 14:29:07.610398: 🔍 接收到串口数据: aa 00 c8 80 00 00 27 82
2025-08-27 14:29:07.611395: 🔍 数据长度: 8 字节
2025-08-27 14:29:07.611395: 🔍 预定义命令列表:
2025-08-27 14:29:07.612392:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 14:29:07.612392:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 14:29:07.612392:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 14:29:07.613392:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 14:29:07.613392:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 14:29:07.613392:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 14:29:07.613392:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 14:29:07.614384:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 14:29:07.614384:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 14:29:07.614384:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 14:29:07.614384: ✅ 解析到闸机命令: GateCommand.exitStart
2025-08-27 14:29:07.615380: 解析到闸机命令: exit_start (出馆开始)
2025-08-27 14:29:07.615380: 收到闸机命令: exit_start (出馆开始)
2025-08-27 14:29:07.615380: 🚪 收到出馆开始命令，等待出馆到位信号...
2025-08-27 14:29:07.616376: 闸机状态变更: GateState.idle -> GateState.exitStarted
2025-08-27 14:29:07.616376: 闸机状态更新: GateState.idle -> GateState.exitStarted
2025-08-27 14:29:07.616376: 📊 流程状态：出馆流程已开始，等待到位信号
2025-08-27 14:29:07.617373: [channel_2] 收到闸机事件: state_changed
2025-08-27 14:29:07.663763: 📨 收到GateCoordinator事件: state_changed
2025-08-27 14:29:07.664760: 闸机状态变更: GateState.exitStarted
2025-08-27 14:29:07.664760: 🎨 处理状态变更UI: exitStarted
2025-08-27 14:29:07.664760: 未处理的状态变更UI: exitStarted
2025-08-27 14:29:07.664760: [channel_2] 收到闸机事件: exit_start
2025-08-27 14:29:07.665756: [channel_2] 主从机扩展：处理出馆开始（请求-响应模式）
2025-08-27 14:29:07.665756: 扫描结果已清空
2025-08-27 14:29:07.665756: 🧹 [channel_2] 已清空RFID服务扫描结果（页面计数重置）
2025-08-27 14:29:07.665756: 📤 [channel_2] 从机出馆开始：请求清空 + 启动持续获取
2025-08-27 14:29:07.666752: 📤 [channel_2] 发送清空请求 (尝试1/2): 983fc7af-692d-4175-b198-37b6eb3fd3b8
2025-08-27 14:29:07.666752: 📨 收到GateCoordinator事件: exit_start
2025-08-27 14:29:07.666752: 页面状态变更: SilencePageState.waitingExit
2025-08-27 14:29:07.666752: ⚠️ 从机模式：忽略本地RFID计数 0
2025-08-27 14:29:07.667749: ⚠️ 从机模式：忽略本地RFID计数 0
2025-08-27 14:29:07.879045: 📨 收到清空响应: 983fc7af-692d-4175-b198-37b6eb3fd3b8 - true
2025-08-27 14:29:07.880048: ✅ [channel_2] 清空请求成功: 清除2个条码
2025-08-27 14:29:07.880048: ✅ [channel_2] 清空请求成功
2025-08-27 14:29:07.880048: 🔄 [channel_2] 启动持续数据获取...
2025-08-27 14:29:07.881039: 🔄 [channel_2] 数据收集定时器已启动（每500ms）
2025-08-27 14:29:07.881039: ✅ [channel_2] 持续数据获取已启动
2025-08-27 14:29:08.380966: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:08.381963: 📨 收到数据响应: b5019f33-9107-47a4-bdd3-c3a865e79ea7 - true
2025-08-27 14:29:08.382959: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:08.382959: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:08.382959: 📥 [channel_2] 收集到新数据: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:08.382959: 📊 [channel_2] 累计收集: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:08.383955: [channel_2] 通知收集到的条码: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:08.383955: ✅ [channel_2] 数据流通知发送成功: 2个条码
2025-08-27 14:29:08.384953: 🎯 持续数据监听触发: 2个条码
2025-08-27 14:29:08.385950: 🎯 _handleMasterSlaveData 被调用: 2个条码
2025-08-27 14:29:08.385950: 📊 当前模式: 主从机=true, 主机=false
2025-08-27 14:29:08.386946: 📊 当前页面状态: SilencePageState.waitingExit
2025-08-27 14:29:08.386946: 📥 从机接收到数据: 2个条码 - [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:08.387942: 📊 扫描列表更新: 0 -> 2
2025-08-27 14:29:08.387942: 📱 更新页面显示: 2个条码
2025-08-27 14:29:08.387942: 页面状态变更: SilencePageState.rfidScanning
2025-08-27 14:29:08.388940: ✅ 从机数据处理完成
2025-08-27 14:29:08.880303: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:08.881300: 📨 收到数据响应: 5779b70e-8b5e-4b18-9715-071f240d4957 - true
2025-08-27 14:29:08.881300: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:08.882296: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:09.381632: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:09.382635: 📨 收到数据响应: 201206ad-3081-4228-9fe2-90a1336f72ed - true
2025-08-27 14:29:09.382635: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:09.383627: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:09.880969: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:09.881966: 📨 收到数据响应: 206f55be-5a1b-4f55-b834-6595944008f9 - true
2025-08-27 14:29:09.881966: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:09.882963: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:10.381303: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:10.382301: 📨 收到数据响应: 35efc4ea-7169-4e63-affe-b35c35cd7990 - true
2025-08-27 14:29:10.382301: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:10.382301: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:10.881188: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:10.882190: 📨 收到数据响应: 902c393b-e235-4e4a-a952-60d6cc32ceb6 - true
2025-08-27 14:29:10.883182: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:10.883182: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:11.381522: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:11.383518: 📨 收到数据响应: 9be12d0b-dc3b-4f6b-b71a-fc6951a915b9 - true
2025-08-27 14:29:11.383518: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:11.384514: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:11.881406: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:11.881406: 📨 收到数据响应: 265adc0b-84cb-43bd-9924-18d92031c1c6 - true
2025-08-27 14:29:11.882404: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:11.882404: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:12.381740: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:12.382738: 📨 收到数据响应: 5420d1aa-4dbb-48c2-a7d5-25c321986a22 - true
2025-08-27 14:29:12.382738: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:12.383734: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:12.881077: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:12.882074: 📨 收到数据响应: dfc737c1-59a0-4655-8b35-690480141c84 - true
2025-08-27 14:29:12.883075: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:12.883075: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:13.381411: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:13.382408: 📨 收到数据响应: ae57bfed-eaeb-4df4-a488-65024fcafe4c - true
2025-08-27 14:29:13.382408: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:13.382408: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:13.881309: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:13.882306: 📨 收到数据响应: 83e4c72a-bca1-420f-99ef-fed5b1b4d9c8 - true
2025-08-27 14:29:13.882306: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:13.883308: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:14.381642: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:14.382640: 📨 收到数据响应: 9e185132-2b21-4cc4-8b91-44591cbbd7ec - true
2025-08-27 14:29:14.382640: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:14.383636: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:14.467358: 接收到数据: aa 00 0a 80 00 00 1a 3a
2025-08-27 14:29:14.468354: 🔍 接收到串口数据: aa 00 0a 80 00 00 1a 3a
2025-08-27 14:29:14.469351: 🔍 数据长度: 8 字节
2025-08-27 14:29:14.469351: 🔍 预定义命令列表:
2025-08-27 14:29:14.469351:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 14:29:14.469351:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 14:29:14.470347:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 14:29:14.470347:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 14:29:14.470347:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 14:29:14.470347:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 14:29:14.470347:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 14:29:14.471344:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 14:29:14.471344:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 14:29:14.471344:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 14:29:14.471344: ✅ 解析到闸机命令: GateCommand.reachPosition
2025-08-27 14:29:14.472340: 解析到闸机命令: position_reached (到达指定位置)
2025-08-27 14:29:14.473338: 收到闸机命令: position_reached (到达指定位置)
2025-08-27 14:29:14.474334: 📍 收到到位信号，当前状态: GateState.exitStarted
2025-08-27 14:29:14.474334: 📊 流程状态：进馆=false, 出馆=true
2025-08-27 14:29:14.474334: 📊 待处理认证：进馆=false, 出馆=false
2025-08-27 14:29:14.475331: 🚪 出馆到位信号，启动认证和10秒数据收集...
2025-08-27 14:29:14.475331: 闸机状态变更: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-27 14:29:14.475331: 闸机状态更新: GateState.exitStarted -> GateState.exitWaitingAuth
2025-08-27 14:29:14.475331: 🔐 启动出馆认证系统（不关注结果）...
2025-08-27 14:29:14.476327: 闸机状态变更: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-27 14:29:14.476327: 闸机状态更新: GateState.exitWaitingAuth -> GateState.exitScanning
2025-08-27 14:29:14.476327: 多认证管理器状态变更: listening
2025-08-27 14:29:14.476327: 启动所有认证方式监听: [AuthMethod.readerCard]
2025-08-27 14:29:14.477324: 准备启动 1 个物理认证服务
2025-08-27 14:29:14.477324: 开始读卡器认证监听
2025-08-27 14:29:14.477324: 🔥 测试：跳过强制重新配置，保持现有连接
2025-08-27 14:29:14.477324: 已移除读卡器状态监听器
2025-08-27 14:29:14.478320: 已移除标签数据监听器
2025-08-27 14:29:14.478320: 所有卡片监听器已移除
2025-08-27 14:29:14.478320: 已添加读卡器状态监听器
2025-08-27 14:29:14.478320: 已添加标签数据监听器
2025-08-27 14:29:14.478320: 开始监听卡片数据 - 所有监听器已就绪
2025-08-27 14:29:14.479317: 读卡器认证监听启动成功
2025-08-27 14:29:14.479317: ✅ 出馆认证系统已启动
2025-08-27 14:29:14.479317: 🚀 启动出馆10秒数据收集...
2025-08-27 14:29:14.480314: 🔧 启动10秒计时器，当前时间: 2025-08-27 14:29:14.472340
2025-08-27 14:29:14.480314: 🔧 10秒计时器已设置，计时器对象: Instance of '_Timer'
2025-08-27 14:29:14.480314: 📡 开始从共享池收集数据...
2025-08-27 14:29:14.480314: 🔧 RFID扫描已在运行，只清空共享池准备收集新数据
2025-08-27 14:29:14.481311: 🧹 开始清空共享扫描池和所有缓冲区...
2025-08-27 14:29:14.481311: 📡 检测到LSGate设备配置: readerType=22
2025-08-27 14:29:14.481311: 🧹 检测到LSGate设备，开始清空硬件缓冲区...
2025-08-27 14:29:14.481311: clearLSGateCache newPort:null
2025-08-27 14:29:14.481311: 📊 LSGate硬件缓存清空命令发送结果: 0
2025-08-27 14:29:14.482307: [channel_2] 收到闸机事件: state_changed
2025-08-27 14:29:14.482307: 📨 收到GateCoordinator事件: state_changed
2025-08-27 14:29:14.482307: 闸机状态变更: GateState.exitWaitingAuth
2025-08-27 14:29:14.482307: 🎨 处理状态变更UI: exitWaitingAuth
2025-08-27 14:29:14.483304: 未处理的状态变更UI: exitWaitingAuth
2025-08-27 14:29:14.483304: 读者证 认证服务启动成功
2025-08-27 14:29:14.483304: 所有认证服务启动完成，成功启动 1 个服务
2025-08-27 14:29:14.483304: 当前可用的认证方式: 读者证
2025-08-27 14:29:14.484301: [channel_2] 收到闸机事件: state_changed
2025-08-27 14:29:14.484301: 📨 收到GateCoordinator事件: state_changed
2025-08-27 14:29:14.484301: 闸机状态变更: GateState.exitScanning
2025-08-27 14:29:14.484301: 🎨 处理状态变更UI: exitScanning
2025-08-27 14:29:14.485297: 页面状态变更: SilencePageState.rfidScanning
2025-08-27 14:29:14.674667: ✅ LSGate硬件缓冲区清空完成
2025-08-27 14:29:14.675664: 🧹 开始清空共享扫描池...
2025-08-27 14:29:14.676660: 📊 清空前状态: 大小=0, 内容=[]
2025-08-27 14:29:14.676660: 🔄 重置RFID去重集合...
2025-08-27 14:29:14.677657: 🔄 开始重置已处理条码集合...
2025-08-27 14:29:14.677657: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 14:29:14.677657: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 14:29:14.678653: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 14:29:14.678653: 📊 当前tagList状态: 0个标签
2025-08-27 14:29:14.678653: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 14:29:14.679650: ✅ 已重置RFID去重集合，现有标签将被重新识别
2025-08-27 14:29:14.679650: 📡 RFID扫描状态（清空后）: isScanning=false
2025-08-27 14:29:14.679650: ⚠️ RFID未在扫描状态，尝试启动数据收集以恢复轮询...
2025-08-27 14:29:14.680647: ⚠️ 从机模式：跳过本地RFID数据收集，等待主机数据同步
2025-08-27 14:29:14.680647: ✅ 共享扫描池已清空: 0 -> 0
2025-08-27 14:29:14.680647: 📡 清空通知已发送，等待RFID重新检测标签...
2025-08-27 14:29:14.681643: 清空RFID扫描缓冲区...
2025-08-27 14:29:14.681643: 🧹 已清空HWTagProvider: 0 -> 0个标签
2025-08-27 14:29:14.681643: 🔧 主机模式：清空软件缓冲区，保持硬件持续扫描
2025-08-27 14:29:14.682640: 🔄 开始重置已处理条码集合...
2025-08-27 14:29:14.682640: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 14:29:14.682640: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 14:29:14.683637: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 14:29:14.683637: 📊 当前tagList状态: 0个标签
2025-08-27 14:29:14.683637: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 14:29:14.684633: ✅ 软件缓冲区已清空，硬件扫描保持运行
2025-08-27 14:29:14.684633: ✅ 已启动RFID数据收集（恢复轮询）
2025-08-27 14:29:14.685631: 🔄 开始重置已处理条码集合...
2025-08-27 14:29:14.685631: 📊 重置前状态: 大小=0, 内容=[]
2025-08-27 14:29:14.685631: ✅ 已处理条码集合已重置: 0 -> 0
2025-08-27 14:29:14.685631: 🔄 当前场上标签将被重新识别为新标签
2025-08-27 14:29:14.686628: 📊 当前tagList状态: 0个标签
2025-08-27 14:29:14.686628: ⚠️ 当前未在扫描状态，跳过立即轮询
2025-08-27 14:29:14.686628: ✅ 共享扫描池、RFID缓冲区和LSGate硬件缓冲区已清空
2025-08-27 14:29:14.881976: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:14.882973: 📨 收到数据响应: f920b960-6bdf-416e-92d5-c69d5569cfb4 - true
2025-08-27 14:29:14.882973: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:14.883976: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:15.381314: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:15.382312: 📨 收到数据响应: 9aa77ec0-8a12-49c6-bc0d-fabab4a10ef4 - true
2025-08-27 14:29:15.383309: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:15.383309: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:15.881647: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:15.882645: 📨 收到数据响应: 0ec7609b-edf5-49f4-8878-0ee95dae1f79 - true
2025-08-27 14:29:15.882645: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:15.883641: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:16.380984: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:16.381981: 📨 收到数据响应: d21d27e7-8866-48c9-b26c-f6fe84da11b8 - true
2025-08-27 14:29:16.382979: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:16.382979: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:16.880879: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:16.881875: 📨 收到数据响应: 976d77e3-6dc0-4bb7-a6a8-5c6254e0921f - true
2025-08-27 14:29:16.881875: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:16.882875: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:17.381213: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:17.382210: 📨 收到数据响应: 603f0afb-ce54-43b7-883e-857b5b1e7563 - true
2025-08-27 14:29:17.382210: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:17.382210: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:17.880550: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:17.881546: 📨 收到数据响应: ad9a2a66-11ae-4cf1-99d9-70c21ac8ee4d - true
2025-08-27 14:29:17.881546: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:17.882543: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:18.380467: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:18.381464: 📨 收到数据响应: 62802bc1-b667-4c83-96b3-3a4021932915 - true
2025-08-27 14:29:18.381464: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:18.381464: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:18.880800: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:18.881798: 📨 收到数据响应: 874259b1-6d41-4048-a4d3-0c41c8dd119f - true
2025-08-27 14:29:18.881798: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:18.881798: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:19.381136: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:19.382133: 📨 收到数据响应: efe416d7-9fa7-4321-a1fa-e50dcbbaac2b - true
2025-08-27 14:29:19.383130: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:19.383130: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:19.881009: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:19.882006: 📨 收到数据响应: dac9854a-c3d6-4f15-b1e6-b4b8a92c8c3f - true
2025-08-27 14:29:19.882006: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:19.882006: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:20.381344: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:20.382341: 📨 收到数据响应: 537d2d99-ea48-45e4-843c-a603de7c631b - true
2025-08-27 14:29:20.383337: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:20.383337: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:20.547789: 接收到数据: aa 00 c9 80 00 00 26 7e
2025-08-27 14:29:20.548799: 🔍 接收到串口数据: aa 00 c9 80 00 00 26 7e
2025-08-27 14:29:20.548799: 🔍 数据长度: 8 字节
2025-08-27 14:29:20.549782: 🔍 预定义命令列表:
2025-08-27 14:29:20.549782:   GateCommand.enterStart: aa 00 64 80 00 00 06 d2
2025-08-27 14:29:20.549782:   GateCommand.enterEnd: aa 00 65 80 00 00 07 2e
2025-08-27 14:29:20.549782:   GateCommand.exitStart: aa 00 c8 80 00 00 27 82
2025-08-27 14:29:20.550779:   GateCommand.exitEnd: aa 00 c9 80 00 00 26 7e
2025-08-27 14:29:20.550779:   GateCommand.reachPosition: aa 00 0a 80 00 00
2025-08-27 14:29:20.550779:   GateCommand.enterOpen: aa 00 02 01 00 00 48 72
2025-08-27 14:29:20.550779:   GateCommand.exitOpen: aa 00 01 01 00 00 48 36
2025-08-27 14:29:20.551775:   GateCommand.failSignal: aa 00 02 01 00 00 48 72
2025-08-27 14:29:20.551775:   GateCommand.tailgating: aa 00 0f 80 00 00
2025-08-27 14:29:20.551775:   GateCommand.doorHasPerson: aa 00 0b 80 00 00
2025-08-27 14:29:20.551775: ✅ 解析到闸机命令: GateCommand.exitEnd
2025-08-27 14:29:20.551775: 解析到闸机命令: exit_end (出馆结束)
2025-08-27 14:29:20.552772: 收到闸机命令: exit_end (出馆结束)
2025-08-27 14:29:20.552772: 出馆流程结束
2025-08-27 14:29:20.552772: 闸机状态变更: GateState.exitScanning -> GateState.idle
2025-08-27 14:29:20.553769: 闸机状态更新: GateState.exitScanning -> GateState.idle
2025-08-27 14:29:20.553769: 📊 流程状态已清除：进馆=false, 出馆=false
2025-08-27 14:29:20.553769: [channel_2] 收到闸机事件: state_changed
2025-08-27 14:29:20.554766: 📨 收到GateCoordinator事件: state_changed
2025-08-27 14:29:20.554766: 闸机状态变更: GateState.idle
2025-08-27 14:29:20.555762: 🎨 处理状态变更UI: idle
2025-08-27 14:29:20.555762: 页面状态变更: SilencePageState.welcome
2025-08-27 14:29:20.555762: [channel_2] 收到闸机事件: exit_end
2025-08-27 14:29:20.556759: [channel_2] 主从机扩展：处理出馆结束
2025-08-27 14:29:20.556759: [channel_2] 清空处理队列，当前大小: 0
2025-08-27 14:29:20.556759: [channel_2] 处理队列已清空
2025-08-27 14:29:20.557756: 📨 收到GateCoordinator事件: exit_end
2025-08-27 14:29:20.557756: 页面状态变更: SilencePageState.welcome
2025-08-27 14:29:20.557756: [channel_2] 通知收集到的条码: []
2025-08-27 14:29:20.557756: ✅ [channel_2] 数据流通知发送成功: 0个条码
2025-08-27 14:29:20.558752: 🎯 持续数据监听触发: 0个条码
2025-08-27 14:29:20.558752: 🎯 _handleMasterSlaveData 被调用: 0个条码
2025-08-27 14:29:20.558752: 📊 当前模式: 主从机=true, 主机=false
2025-08-27 14:29:20.559750: 📊 当前页面状态: SilencePageState.welcome
2025-08-27 14:29:20.559750: 📥 从机接收到数据: 0个条码 - []
2025-08-27 14:29:20.559750: 📊 扫描列表更新: 2 -> 0
2025-08-27 14:29:20.559750: ⚠️ 页面状态不是扫描相关状态，跳过页面更新: SilencePageState.welcome
2025-08-27 14:29:20.559750: ✅ 从机数据处理完成
2025-08-27 14:29:20.880680: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:20.881678: 📨 收到数据响应: 71cb2d8e-7fec-4849-b36c-02e1bb766406 - true
2025-08-27 14:29:20.881678: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:20.882674: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:21.381014: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:21.382011: 📨 收到数据响应: 68712ca6-a2ca-4efe-b2e0-4152bc36005d - true
2025-08-27 14:29:21.382011: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:21.382011: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:21.881348: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:21.882345: 📨 收到数据响应: f3ce8759-df2f-4bee-bc3f-3dd4a099dacb - true
2025-08-27 14:29:21.882345: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:21.883342: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:22.381681: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:22.382678: 📨 收到数据响应: 47349c2f-9400-411a-a582-f3523aab0d49 - true
2025-08-27 14:29:22.382678: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:22.382678: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:22.880578: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:22.881575: 📨 收到数据响应: 66da7325-96bd-46b8-93b8-1b32c6d51829 - true
2025-08-27 14:29:22.881575: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:22.881575: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:23.380915: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:23.382915: 📨 收到数据响应: 86485e48-69de-4dcb-acc5-6ff4f6de8263 - true
2025-08-27 14:29:23.382915: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:23.383903: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:23.881245: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:23.882242: 📨 收到数据响应: 5f90e601-9bd3-4d92-a809-ec70bbc35680 - true
2025-08-27 14:29:23.882242: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:23.882242: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:24.381579: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:24.382578: 📨 收到数据响应: cdcd2421-eb33-4e97-b7f5-bc9f299137e3 - true
2025-08-27 14:29:24.382578: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:24.382578: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:24.881913: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:24.881913: 📨 收到数据响应: 312d0399-c5f5-4a23-872b-54fcd239948e - true
2025-08-27 14:29:24.882910: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:24.882910: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:25.381250: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:25.382247: 📨 收到数据响应: e7e142f5-b709-4f2f-8ef5-9b30c94b5dd0 - true
2025-08-27 14:29:25.382247: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:25.382247: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:25.881111: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:25.882109: 📨 收到数据响应: 0abe8eef-ae81-466a-92ab-f9abdd121b59 - true
2025-08-27 14:29:25.882109: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:25.882109: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:26.381445: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:26.382442: 📨 收到数据响应: c74924df-545f-4841-b6f3-693eff90355c - true
2025-08-27 14:29:26.382442: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:26.382442: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:26.881779: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:26.881779: 📨 收到数据响应: 1a1b9ab9-ad26-465d-89bd-2927fe1353c0 - true
2025-08-27 14:29:26.882776: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:26.882776: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:27.381116: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:27.382113: 📨 收到数据响应: 30e64454-bfa5-47fe-9289-b41d856a44c0 - true
2025-08-27 14:29:27.382113: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:27.382113: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:27.880962: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:27.881959: 📨 收到数据响应: 287d2211-82f1-479e-8ca2-921d09714836 - true
2025-08-27 14:29:27.881959: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:27.881959: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
2025-08-27 14:29:28.380842: 📤 [channel_2] 请求主机当前数据 (尝试1/1)...
2025-08-27 14:29:28.380842: 📨 收到数据响应: 0698298d-b4e6-4bca-81ff-748b3289e42e - true
2025-08-27 14:29:28.381839: ✅ [channel_2] 获取主机数据成功: 2个条码
2025-08-27 14:29:28.381839: 📋 [channel_2] 数据详情: [E004015304F3DD22, E004015305F68508]
